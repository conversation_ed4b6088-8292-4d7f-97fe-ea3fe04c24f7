#!/usr/bin/env pwsh
# NetScaler CVE-2025-6543 Testing Automation Script
# Author: Senior Cybersecurity Engineer
# Purpose: Automated testing of Nuclei template against vulnerable NetScaler

param(
    [string]$TemplatePath = "http/cves/2025/CVE-2025-6543.yaml",
    [string]$ContainerName = "vulnerable-netscaler",
    [string]$NetScalerVersion = "13.1-51.15",
    [int]$WaitTime = 120
)

# Color functions for better output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Step { param($Step, $Message) Write-Host "🔄 Step $Step`: $Message" -ForegroundColor Magenta }

Write-Host "🚀 NetScaler CVE-2025-6543 Automated Testing Workflow" -ForegroundColor Blue
Write-Host "=" * 60 -ForegroundColor Blue

# Step 1: Check Prerequisites
Write-Step 1 "Checking Prerequisites"
try {
    # Check if Docker is installed and running
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Docker is not installed or not in PATH"
        exit 1
    }
    Write-Success "Docker found: $dockerVersion"

    # Check if Nuclei is installed
    $nucleiVersion = nuclei -version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Nuclei is not installed or not in PATH"
        exit 1
    }
    Write-Success "Nuclei found: $nucleiVersion"

    # Check if template exists
    if (-not (Test-Path $TemplatePath)) {
        Write-Error "Template not found: $TemplatePath"
        exit 1
    }
    Write-Success "Template found: $TemplatePath"
}
catch {
    Write-Error "Prerequisites check failed: $_"
    exit 1
}

# Step 2: Clean up existing container
Write-Step 2 "Cleaning up existing containers"
try {
    $existingContainer = docker ps -a --filter "name=$ContainerName" --format "{{.Names}}" 2>$null
    if ($existingContainer -eq $ContainerName) {
        Write-Info "Stopping and removing existing container: $ContainerName"
        docker stop $ContainerName 2>$null | Out-Null
        docker rm $ContainerName 2>$null | Out-Null
        Write-Success "Existing container cleaned up"
    } else {
        Write-Info "No existing container to clean up"
    }
}
catch {
    Write-Warning "Container cleanup had issues, continuing..."
}

# Step 3: Pull Docker Image
Write-Step 3 "Pulling NetScaler Docker Image"
try {
    Write-Info "Pulling quay.io/citrix/citrix-adc-cpx:$NetScalerVersion"
    docker pull "quay.io/citrix/citrix-adc-cpx:$NetScalerVersion"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to pull Docker image"
        exit 1
    }
    Write-Success "Docker image pulled successfully"
}
catch {
    Write-Error "Failed to pull Docker image: $_"
    exit 1
}

# Step 4: Run Docker Container
Write-Step 4 "Starting NetScaler Container"
try {
    Write-Info "Starting container with name: $ContainerName"
    $containerId = docker run -dt `
        --name $ContainerName `
        --cap-add=NET_ADMIN `
        -p 80:80 `
        -p 443:443 `
        -p 9080:9080 `
        -e EULA=yes `
        "quay.io/citrix/citrix-adc-cpx:$NetScalerVersion"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to start container"
        exit 1
    }
    Write-Success "Container started with ID: $($containerId.Substring(0,12))"
}
catch {
    Write-Error "Failed to start container: $_"
    exit 1
}

# Step 5: Wait for NetScaler to Initialize
Write-Step 5 "Waiting for NetScaler to Initialize"
Write-Info "NetScaler needs time to start up completely. Waiting $WaitTime seconds..."
$progressParams = @{
    Activity = "Initializing NetScaler"
    Status = "Please wait while NetScaler starts up..."
    PercentComplete = 0
}

for ($i = 1; $i -le $WaitTime; $i++) {
    $progressParams.PercentComplete = ($i / $WaitTime) * 100
    $progressParams.Status = "Waiting... $i/$WaitTime seconds"
    Write-Progress @progressParams
    Start-Sleep 1
}
Write-Progress -Activity "Initializing NetScaler" -Completed
Write-Success "Wait period completed"

# Step 6: Verify Container Status
Write-Step 6 "Verifying Container Status"
try {
    $containerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"
    if ($containerStatus -like "*Up*") {
        Write-Success "Container is running: $containerStatus"
    } else {
        Write-Error "Container is not running properly"
        docker logs $ContainerName --tail 20
        exit 1
    }
}
catch {
    Write-Error "Failed to verify container status: $_"
    exit 1
}

# Step 7: Test NetScaler Connectivity
Write-Step 7 "Testing NetScaler Connectivity"
$maxRetries = 10
$retryCount = 0
$connected = $false

while ($retryCount -lt $maxRetries -and -not $connected) {
    try {
        Write-Info "Testing connectivity (attempt $($retryCount + 1)/$maxRetries)..."
        
        # Test HTTP connectivity
        $httpResponse = Invoke-WebRequest -Uri "http://127.0.0.1" -Method Head -TimeoutSec 10 -SkipCertificateCheck -ErrorAction Stop
        Write-Success "HTTP connectivity confirmed (Status: $($httpResponse.StatusCode))"
        
        # Test HTTPS connectivity
        $httpsResponse = Invoke-WebRequest -Uri "https://127.0.0.1" -Method Head -TimeoutSec 10 -SkipCertificateCheck -ErrorAction Stop
        Write-Success "HTTPS connectivity confirmed (Status: $($httpsResponse.StatusCode))"
        
        $connected = $true
    }
    catch {
        $retryCount++
        if ($retryCount -lt $maxRetries) {
            Write-Warning "Connection attempt failed, retrying in 10 seconds... ($_)"
            Start-Sleep 10
        } else {
            Write-Error "Failed to connect to NetScaler after $maxRetries attempts"
            Write-Info "Container logs:"
            docker logs $ContainerName --tail 20
            exit 1
        }
    }
}

# Step 8: Execute Nuclei Scan
Write-Step 8 "Executing Nuclei Scan"
try {
    Write-Info "Running Nuclei with template: $TemplatePath"
    Write-Info "Target: https://127.0.0.1"
    Write-Host "`n" + "="*60 + " NUCLEI SCAN RESULTS " + "="*60 -ForegroundColor Yellow
    
    # Execute Nuclei scan
    nuclei -t $TemplatePath -u "https://127.0.0.1" -debug -v
    
    $nucleiExitCode = $LASTEXITCODE
    Write-Host "`n" + "="*60 + " END SCAN RESULTS " + "="*60 -ForegroundColor Yellow
    
    if ($nucleiExitCode -eq 0) {
        Write-Success "Nuclei scan completed successfully"
    } else {
        Write-Warning "Nuclei scan completed with exit code: $nucleiExitCode"
    }
}
catch {
    Write-Error "Failed to execute Nuclei scan: $_"
}

# Step 9: Cleanup
Write-Step 9 "Cleanup"
try {
    Write-Info "Stopping and removing container: $ContainerName"
    docker stop $ContainerName 2>$null | Out-Null
    docker rm $ContainerName 2>$null | Out-Null
    Write-Success "Container cleaned up successfully"
}
catch {
    Write-Warning "Cleanup had issues: $_"
}

Write-Host "`n🎉 NetScaler CVE Testing Automation Complete!" -ForegroundColor Green
Write-Host "Summary:" -ForegroundColor Blue
Write-Host "- Template: $TemplatePath" -ForegroundColor White
Write-Host "- Target: NetScaler CPX $NetScalerVersion" -ForegroundColor White
Write-Host "- Container: $ContainerName (cleaned up)" -ForegroundColor White
